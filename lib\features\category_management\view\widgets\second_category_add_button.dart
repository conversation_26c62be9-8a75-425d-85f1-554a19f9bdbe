import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

class SecondCategoryAddButton extends StatelessWidget {
  final VoidCallback onTap;

  const SecondCategoryAddButton({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onTap,
      icon: Container(
        decoration: BoxDecoration(
          color: AppColors.circleButtonColor,
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.add_rounded, color: Colors.white, size: 28.sp),
      ),
    );
  }
}
