import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/custom_warehouse_card.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/edit_warehouse_dialog.dart';

class WarehouseManagementPage extends StatefulWidget {
  const WarehouseManagementPage({super.key});

  @override
  State<WarehouseManagementPage> createState() =>
      _WarehouseManagementPageState();
}

class _WarehouseManagementPageState extends State<WarehouseManagementPage> {
  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.scaffoldBackgroundColor,
          title: Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Text("仓库管理"),
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                showEditWarehouseDialog(
                  context: context,
                  initialName: "",
                  initialAddress: "",
                  onNameChanged: (_) {},
                  onConfirm: () {},
                  type: 1,
                );
              },
              icon: Icon(Icons.add_rounded, size: 25.w),
            ),
          ],
        ),
        body: Column(
          children: [
            SizedBox(height: 10.h),
            CustomWarehouseCard(
              name: '工程仓haidhasihdiashdioashdioashdiaoshdioashdiohasidhas',
              address: "广州牛逼酒店牛逼部分ashndiashnidhbasidbnasiobdaisodbaso",
              totalItemAmount: '18273',
              totalMoney: '¥1,293,891',
            ),
            SizedBox(height: 10.h),
            CustomWarehouseCard(
              name: '财务仓',
              address: "广州牛逼酒店牛逼部分",
              totalItemAmount: '18273',
              totalMoney: '¥1,293,891',
            ),
          ],
        ),
      ),
    );
  }
}
