import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/constants/home_data_card.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';

class OutboundPage extends StatefulWidget {
  const OutboundPage({super.key});

  @override
  State<OutboundPage> createState() => _OutboundPageState();
}

class _OutboundPageState extends State<OutboundPage> {
  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.scaffoldBackgroundColor,
          title: Center(
            child: Padding(
              padding: const EdgeInsets.only(left: 40),
              child: Text("出库数据"),
            ),
          ),
          actions: [
            IconButton(onPressed: () {}, icon: Icon(Icons.ios_share_rounded)),
            IconButton(onPressed: () {}, icon: Icon(Icons.search_rounded)),
          ],
        ),
        body: Column(
          children: [
            SizedBox(height: 10.h),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.w),
                color: AppColors.primaryColor,
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "出库总金额",
                      style: TextStyle(
                        color: HomeDataCard.fontColor,
                        fontSize: 13.w,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      "¥230,203.45",
                      style: TextStyle(
                        fontSize: 30.w,
                        fontWeight: FontWeight.bold,
                        color: HomeDataCard.fontColor,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "出库件数",
                          style: TextStyle(
                            color: HomeDataCard.fontColor,
                            fontSize: 12.w,
                          ),
                        ),
                        Text(
                          "1230",
                          style: TextStyle(
                            color: HomeDataCard.fontColor,
                            fontSize: 12.w,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 5.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "日均出库金额",
                          style: TextStyle(
                            color: HomeDataCard.fontColor,
                            fontSize: 12.w,
                          ),
                        ),
                        Text(
                          "¥2,394.98",
                          style: TextStyle(
                            color: HomeDataCard.fontColor,
                            fontSize: 12.w,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 5.h),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
