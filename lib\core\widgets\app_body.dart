import 'package:flutter/material.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

class AppBody extends StatelessWidget {
  final Widget child;
  const AppBody({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.scaffoldBackgroundColor,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: child,
      ),
    );
  }
}
