import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/constants/operation_type.dart';

class RecentOperationItemCard extends StatefulWidget {
  final String name;
  final String englishName;
  final String model;
  final String operationTime;
  final String operator;
  final OperationType operationType;

  const RecentOperationItemCard({
    super.key,
    required this.name,
    required this.englishName,
    required this.model,
    required this.operationTime,
    required this.operator,
    required this.operationType,
  });

  @override
  State<RecentOperationItemCard> createState() =>
      _RecentOperationItemCardState();
}

class _RecentOperationItemCardState extends State<RecentOperationItemCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.w),
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            SizedBox(
              width: 65.w,
              child: AspectRatio(
                aspectRatio: 1,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.w),
                  child: Image.asset(
                    // TODO 后期图片要从服务器获取
                    "assets/images/test.jpg",
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: SizedBox(
                          child: Text(
                            widget.name,
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      ItemOperation.getOperationIcon(widget.operationType),
                    ],
                  ),
                  Text(widget.englishName),
                  Text("型号: ${widget.model}"),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("操作时间: ${widget.operationTime}"),
                      Row(
                        children: [
                          Icon(Icons.person_pin, color: Colors.blueGrey),
                          Text(widget.operator),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
