import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/config/app_routes.dart';
import 'package:inventory_app_final/core/constants/home_data_card.dart';
import 'package:inventory_app_final/core/constants/operation_type.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';
import 'package:inventory_app_final/core/widgets/search_box.dart';
import 'package:inventory_app_final/features/home/<USER>/widgets/main_function_area.dart';
import 'package:inventory_app_final/features/home/<USER>/widgets/management_icon_area.dart';
import 'package:inventory_app_final/features/home/<USER>/widgets/recent_operation_item_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  List<String> items = ['工程仓', '财务仓', '餐饮仓', '客房仓'];
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        body: Column(
          children: [
            // 自定义的AppBar,不随页面滚动
            SizedBox(height: 40.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButton(
                  borderRadius: BorderRadius.circular(20),
                  value: items.first,
                  dropdownColor: Colors.white,
                  underline: SizedBox(),
                  items:
                      items.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Center(
                            child: Text(
                              value,
                              style: TextStyle(fontSize: 13.sp),
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: (_) {
                    // TODO 仓库切换
                  },
                ),
                IconButton(
                  onPressed: () {
                    // TODO 点击之后进入消息页面
                  },
                  icon: Icon(Icons.notifications, size: 18.sp),
                ),
              ],
            ),
            SearchBox(
              controller: _searchController,
              onSearch: () {
                Navigator.pushNamed(
                  context,
                  AppRoutes.search,
                  arguments: {'initialSearchText': _searchController.text},
                );
              },
            ),
            SizedBox(height: 10.h),
            Expanded(
              child: RefreshIndicator(
                onRefresh: () {
                  // 模拟网络请求
                  return Future.delayed(Duration(seconds: 1));
                },
                // TODO 这里先用SingleChildScrollView做测试，后期要改成惰性渲染
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // MainFunctionArea(),
                      SizedBox(height: 10.h),
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.w),
                          color: HomeDataCard.backgroundColor,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "库存总金额",
                                style: TextStyle(
                                  color: HomeDataCard.fontColor,
                                  fontSize: 13.w,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Text(
                                "¥230,203.45",
                                style: TextStyle(
                                  fontSize: 30.w,
                                  fontWeight: FontWeight.bold,
                                  color: HomeDataCard.fontColor,
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "本月出库金额",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                  Text(
                                    "¥30,429.45",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "本月入库金额",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                  Text(
                                    "¥2,394.98",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "今日出库件数",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                  Text(
                                    "20",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "今日出库金额",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                  Text(
                                    "¥398.98",
                                    style: TextStyle(
                                      color: HomeDataCard.fontColor,
                                      fontSize: 12.w,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 10.h),
                      ManagementIconArea(),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text("最近操作"),
                          IconButton(
                            onPressed: () {},
                            icon: Icon(Icons.segment_rounded),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      RecentOperationItemCard(
                        name: "精品螺丝刀中国产厉害厉害",
                        englishName: "This is the english name",
                        model: "M123124141",
                        operationTime: "2023-01-01 12:00:00",
                        operator: "小陈",
                        operationType: OperationType.inbound,
                      ),
                      RecentOperationItemCard(
                        name: "大功率收款墨迹就i久啊四点奶刷到哪是的撒",
                        englishName: "This is the english name",
                        model: "M123124141",
                        operationTime: "2023-01-01 12:00:00",
                        operator: "小陈",
                        operationType: OperationType.outbound,
                      ),
                      RecentOperationItemCard(
                        name: "正泰交流接触器 32A 380V",
                        englishName: "This is the english name",
                        model: "M123124141",
                        operationTime: "2023-01-01 12:00:00",
                        operator: "小陈",
                        operationType: OperationType.edit,
                      ),
                      RecentOperationItemCard(
                        name: "大功率收款墨迹就i久啊四点奶刷到哪是的撒",
                        englishName: "This is the english name",
                        model: "M123124141",
                        operationTime: "2023-01-01 12:00:00",
                        operator: "小陈",
                        operationType: OperationType.add,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
