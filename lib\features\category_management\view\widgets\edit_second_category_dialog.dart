import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

/// type 0 是修改，1 是添加
Future<void> showEditSecondCategoryDialog({
  required BuildContext context,
  required Color selectedColor,
  required String initialName,
  required void Function(Color) onColorSelected,
  required void Function(String) onNameChanged,
  required VoidCallback onConfirm,
  required int type, // 0 是修改，1 是添加
}) async {
  Color currentColor = selectedColor;
  TextEditingController nameController = TextEditingController(
    text: initialName,
  );

  await showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Center(
              child: Text(
                type == 0 ? '修改分类' : '添加二级分类',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
              ),
            ),
            content: SizedBox(
              height: 100.h,
              width: 300.w,
              child: Column(
                children: [
                  // 颜色选择
                  SizedBox(
                    height: 50,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children:
                            AppColors.strongButtonColors.map((color) {
                              bool isSelected = color == currentColor;
                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    currentColor = color;
                                  });
                                  onColorSelected(color);
                                },
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                  ),
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: color,
                                    shape: BoxShape.circle,
                                    border:
                                        isSelected
                                            ? Border.all(
                                              color: Colors.black38,
                                              width: 5,
                                            )
                                            : null,
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),
                  // 名称输入
                  Row(
                    children: [
                      Text("名称:", style: TextStyle(fontSize: 15.sp)),
                      SizedBox(width: 5.w),
                      Expanded(
                        child: TextField(
                          controller: nameController,
                          onChanged: (value) => onNameChanged(value),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.0,
                            ),
                            hintText: '请输入分类名称',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: const BorderSide(color: Colors.grey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                                width: 2.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  '取消',
                  style: TextStyle(fontSize: 14.sp, color: Colors.black54),
                ),
              ),
              TextButton(
                onPressed: () {
                  onConfirm();
                  Navigator.pop(context);
                },
                child: Text(
                  '确定',
                  style: TextStyle(fontSize: 14.sp, color: Colors.black54),
                ),
              ),
            ],
          );
        },
      );
    },
  );
}
