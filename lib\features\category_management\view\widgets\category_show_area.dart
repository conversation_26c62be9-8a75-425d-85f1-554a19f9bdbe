import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoryShowArea extends StatefulWidget {
  final Widget firstCategoryTitle;
  final List<Widget> secondCategoryIconButtonList;

  const CategoryShowArea({
    super.key,
    required this.firstCategoryTitle,
    required this.secondCategoryIconButtonList,
  });

  @override
  State<CategoryShowArea> createState() => _CategoryShowAreaState();
}

class _CategoryShowAreaState extends State<CategoryShowArea> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        widget.firstCategoryTitle,
        Wrap(
          spacing: -5.w,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: widget.secondCategoryIconButtonList,
        ),
      ],
    );
  }
}
