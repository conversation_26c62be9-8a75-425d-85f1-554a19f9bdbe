import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

/// type 0 是修改，1 是添加
Future<void> showEditSupplierDialog({
  required BuildContext context,
  required String initialCompanyName,
  required String initialName,
  required String initialPhoneNumber,
  required void Function(String) onNameChanged,
  required VoidCallback onConfirm,
  required int type, // 0 是修改，1 是添加
}) async {
  TextEditingController companyNameController = TextEditingController(
    text: initialCompanyName,
  );
  TextEditingController nameController = TextEditingController(
    text: initialName,
  );
  TextEditingController phoneNumberController = TextEditingController(
    text: initialPhoneNumber,
  );

  await showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Center(
              child: Text(
                type == 0 ? '修改' : '新增供应商',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
              ),
            ),
            content: SizedBox(
              height: 140.h,
              width: 300.w,
              child: Column(
                children: [
                  SizedBox(height: 10.h),
                  // 名称输入
                  Row(
                    children: [
                      Text("公司名:", style: TextStyle(fontSize: 15.sp)),
                      SizedBox(width: 5.w),
                      Expanded(
                        child: TextField(
                          controller: companyNameController,
                          onChanged: (value) => onNameChanged(value),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.0,
                            ),
                            hintText: '请输入公司名称',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: const BorderSide(color: Colors.grey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                                width: 2.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    children: [
                      Text("联系人:", style: TextStyle(fontSize: 15.sp)),
                      SizedBox(width: 5.w),
                      Expanded(
                        child: TextField(
                          controller: nameController,
                          onChanged: (value) => onNameChanged(value),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.0,
                            ),
                            hintText: '请输入联系人姓名',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: const BorderSide(color: Colors.grey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                                width: 2.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text("电话:", style: TextStyle(fontSize: 15.sp)),
                      SizedBox(width: 20.w),
                      Expanded(
                        child: TextField(
                          controller: phoneNumberController,
                          onChanged: (value) => onNameChanged(value),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.0,
                            ),
                            hintText: '请输入电话号码',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: const BorderSide(color: Colors.grey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                                width: 2.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  '取消',
                  style: TextStyle(fontSize: 14.sp, color: Colors.black54),
                ),
              ),
              TextButton(
                onPressed: () {
                  onConfirm();
                  Navigator.pop(context);
                },
                child: Text(
                  '确定',
                  style: TextStyle(fontSize: 14.sp, color: Colors.black54),
                ),
              ),
            ],
          );
        },
      );
    },
  );
}
