import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

class SearchBox extends StatelessWidget {
  final TextEditingController controller;
  final void Function() onSearch;

  const SearchBox({
    super.key,
    required this.controller,
    required this.onSearch,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        filled: true,
        fillColor: Colors.white,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(vertical: 9.h),
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: SizedBox(
            width: 50.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                IconButton(
                  onPressed: () {
                    // TODO 点击进入扫描的界面
                  },
                  icon: Icon(
                    Icons.camera_alt_rounded,
                    size: 23.w,
                    color: AppColors.primaryColor,
                  ),
                ),
                Container(width: 1, height: 20.h, color: Colors.grey),
              ],
            ),
          ),
        ),
        hintText: "物品/型号/分类/供应商",
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30.r),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30.r),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2.w),
        ),
        suffixIcon: TextButton(
          onPressed: onSearch,
          child: Text(
            "搜索",
            style: TextStyle(fontSize: 13.w, color: Colors.black54),
          ),
        ),
      ),
    );
  }
}
