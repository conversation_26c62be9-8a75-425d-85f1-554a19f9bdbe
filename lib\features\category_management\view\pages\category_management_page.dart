import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/category_show_area.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/category_title.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/custom_text_button.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/edit_second_category_dialog.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/edit_first_category_name_dialog.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/second_category_add_button.dart';

class CategoryManagementPage extends StatefulWidget {
  const CategoryManagementPage({super.key});

  @override
  State<CategoryManagementPage> createState() => _CategoryManagementPageState();
}

class _CategoryManagementPageState extends State<CategoryManagementPage> {
  final List<String> groupNames = ['组1', '组2', '组3', '组4'];

  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.scaffoldBackgroundColor,
          title: Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Text("分类管理"),
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                showEditFirstCategoryDialog(
                  context: context,
                  initialName: "",
                  onNameChanged: (_) {},
                  onConfirm: () {},
                  type: 1,
                );
              },
              icon: Icon(Icons.add_rounded, size: 25.w),
            ),
          ],
        ),
        body: Column(
          children: [
            // TODO 真实数据循环显示这个组件即可
            CategoryShowArea(
              firstCategoryTitle: CategoryTitle(
                title: "机修",
                onEdit: () {
                  showEditFirstCategoryDialog(
                    context: context,
                    initialName: "机修",
                    onNameChanged: (_) {},
                    onConfirm: () {},
                    type: 0,
                  );
                },
              ),
              secondCategoryIconButtonList: [
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[1],
                  text: "空调",
                  onTap: () {
                    showEditSecondCategoryDialog(
                      context: context,
                      selectedColor: AppColors.strongButtonColors[0],
                      initialName: "原来的名称",
                      onColorSelected: (_) {},
                      onNameChanged: (_) {},
                      onConfirm: () {},
                      type: 0,
                    );
                  },
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[10],
                  text: "管道",
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[5],
                  text: "排水",
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[16],
                  text: "厨房",
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[19],
                  text: "工具",
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[19],
                  text: "机修工具",
                ),
                CustomTextButton(
                  backgroundColor: AppColors.strongButtonColors[19],
                  text: "机修工具",
                ),
                SecondCategoryAddButton(
                  onTap: () {
                    // TODO 添加二级分类
                    showEditSecondCategoryDialog(
                      context: context,
                      selectedColor: AppColors.strongButtonColors[0],
                      initialName: "",
                      onColorSelected: (_) {},
                      onNameChanged: (_) {},
                      onConfirm: () {},
                      type: 1,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
