import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/features/category_management/view/widgets/edit_first_category_name_dialog.dart';

class CategoryTitle extends StatelessWidget {
  final String title;
  final VoidCallback? onEdit;

  const CategoryTitle({super.key, required this.title, this.onEdit});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 100.w,
            child: Divider(thickness: 0.5.w, color: Colors.grey),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                Text(title, style: TextStyle(fontSize: 18.sp)),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GestureDetector(
                    onTap: () {
                      showEditFirstCategoryDialog(
                        context: context,
                        initialName: title,
                        onNameChanged: (_) {},
                        onConfirm: () {},
                        type: 0,
                      );
                    },
                    child: Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: AppColors.circleButtonColor,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.edit_rounded,
                        color: Colors.white,
                        size: 15.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 100.w,
            child: Divider(thickness: 0.5.w, color: Colors.grey),
          ),
        ],
      ),
    );
    ;
  }
}
